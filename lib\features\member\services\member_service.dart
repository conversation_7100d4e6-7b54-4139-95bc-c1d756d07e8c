/// 会员信息服务
/// 
/// 提供会员信息的获取、更新等核心业务逻辑
/// 复用现有的NetworkService和CommonResult处理机制
library;

import 'package:flutter/foundation.dart';
import '../../../core/network/network_service.dart';
import '../../../features/auth/models/common_result.dart';
import '../models/member_models.dart';

/// 会员信息服务接口
abstract class MemberServiceInterface {
  /// 获取会员信息
  Future<CommonResult<MemberInfo>> getMemberInfo();

  /// 更新昵称
  Future<CommonResult<bool>> updateNickName(String nickName);

  /// 更新头像
  Future<CommonResult<bool>> updateAvatar(String avatarUrl);
}

/// 会员信息服务实现
class MemberService implements MemberServiceInterface {
  static MemberService? _instance;
  
  /// 网络服务实例
  final NetworkService _networkService = NetworkService.instance;

  /// API端点常量
  static const String _baseEndpoint = '/api/member';
  static const String _getMemberEndpoint = '$_baseEndpoint/get';
  static const String _updateNickNameEndpoint = '$_baseEndpoint/updateNickName';
  static const String _updateAvatarEndpoint = '$_baseEndpoint/updateAvatar';

  MemberService._internal();

  /// 单例模式
  static MemberService get instance {
    _instance ??= MemberService._internal();
    return _instance!;
  }

  /// 获取会员信息
  @override
  Future<CommonResult<MemberInfo>> getMemberInfo() async {
    try {
      debugPrint('正在获取会员信息...');

      // 使用统一的CommonResult处理机制
      final result = await _networkService.getCommonResult<MemberInfo>(
        _getMemberEndpoint,
        fromJson: (json) => MemberInfo.fromJson(json),
      );

      if (result.isSuccess) {
        debugPrint('获取会员信息成功: ${result.data}');
      } else {
        debugPrint('获取会员信息失败: ${result.msg}');
      }

      return result;
    } catch (e, stackTrace) {
      debugPrint('获取会员信息异常: $e');
      debugPrint('错误堆栈: $stackTrace');
      
      return CommonResult.failure(
        code: -1,
        msg: '获取会员信息失败: $e',
      );
    }
  }

  /// 更新昵称
  @override
  Future<CommonResult<bool>> updateNickName(String nickName) async {
    try {
      debugPrint('正在更新昵称: $nickName');

      // 使用GET请求，参数通过queryParameters传递（匹配后端接口）
      final result = await _networkService.getCommonResult<bool>(
        _updateNickNameEndpoint,
        queryParameters: {'nickName': nickName.trim()},
        fromJson: (json) {
          // 后端返回Boolean类型，直接转换
          if (json is bool) return json;
          if (json is String) return json.toLowerCase() == 'true';
          if (json is int) return json == 1;
          return false;
        },
      );

      if (result.isSuccess) {
        debugPrint('更新昵称成功');
      } else {
        debugPrint('更新昵称失败: ${result.msg}');
      }

      return result;
    } catch (e, stackTrace) {
      debugPrint('更新昵称异常: $e');
      debugPrint('错误堆栈: $stackTrace');
      
      return CommonResult.failure(
        code: -1,
        msg: '更新昵称失败: $e',
      );
    }
  }

  /// 更新头像
  @override
  Future<CommonResult<bool>> updateAvatar(String avatarUrl) async {
    try {
      debugPrint('正在更新头像: $avatarUrl');

      // 使用GET请求，参数通过queryParameters传递（匹配后端接口）
      final result = await _networkService.getCommonResult<bool>(
        _updateAvatarEndpoint,
        queryParameters: {'avatarUrl': avatarUrl.trim()},
        fromJson: (json) {
          // 后端返回Boolean类型，直接转换
          if (json is bool) return json;
          if (json is String) return json.toLowerCase() == 'true';
          if (json is int) return json == 1;
          return false;
        },
      );

      if (result.isSuccess) {
        debugPrint('更新头像成功');
      } else {
        debugPrint('更新头像失败: ${result.msg}');
      }

      return result;
    } catch (e, stackTrace) {
      debugPrint('更新头像异常: $e');
      debugPrint('错误堆栈: $stackTrace');
      
      return CommonResult.failure(
        code: -1,
        msg: '更新头像失败: $e',
      );
    }
  }

  /// 批量更新会员信息（扩展方法）
  Future<CommonResult<List<MemberUpdateResult>>> batchUpdateMemberInfo({
    String? nickName,
    String? avatarUrl,
  }) async {
    final results = <MemberUpdateResult>[];

    try {
      // 更新昵称
      if (nickName?.isNotEmpty == true) {
        final nickNameResult = await updateNickName(nickName!);
        results.add(MemberUpdateResult(
          success: nickNameResult.isSuccess,
          operationType: MemberOperationType.updateNickName,
          updateTime: DateTime.now(),
          message: nickNameResult.msg,
        ));
      }

      // 更新头像
      if (avatarUrl?.isNotEmpty == true) {
        final avatarResult = await updateAvatar(avatarUrl!);
        results.add(MemberUpdateResult(
          success: avatarResult.isSuccess,
          operationType: MemberOperationType.updateAvatar,
          updateTime: DateTime.now(),
          message: avatarResult.msg,
        ));
      }

      // 检查是否所有操作都成功
      final allSuccess = results.every((result) => result.success);
      
      return CommonResult.success(
        data: results,
        msg: allSuccess ? '批量更新成功' : '部分更新失败',
      );
    } catch (e) {
      debugPrint('批量更新会员信息异常: $e');
      
      return CommonResult.failure(
        code: -1,
        msg: '批量更新失败: $e',
        data: results,
      );
    }
  }

  /// 刷新会员信息缓存（如果有缓存机制）
  Future<void> refreshMemberInfoCache() async {
    // 这里可以实现缓存刷新逻辑
    // 例如清除本地缓存，强制重新获取
    debugPrint('刷新会员信息缓存');
  }

  /// 检查会员信息是否需要更新
  Future<bool> needsUpdate(MemberInfo currentInfo) async {
    try {
      final latestResult = await getMemberInfo();
      if (latestResult.isSuccess && latestResult.data != null) {
        // 比较关键字段是否有变化
        final latest = latestResult.data!;
        return currentInfo.nickName != latest.nickName ||
               currentInfo.avatarUrl != latest.avatarUrl;
      }
      return false;
    } catch (e) {
      debugPrint('检查会员信息更新状态异常: $e');
      return false;
    }
  }
}
